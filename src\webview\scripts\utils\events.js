/**
 * 事件管理系统
 * 提供发布订阅模式的事件管理功能
 */

/**
 * 事件管理器类
 */
export class EventManager {
  constructor() {
    this.events = new Map();
    this.onceEvents = new Map();
    this.maxListeners = 10;
    
    // 绑定方法上下文
    this.on = this.on.bind(this);
    this.off = this.off.bind(this);
    this.emit = this.emit.bind(this);
    this.once = this.once.bind(this);
  }

  /**
   * 添加事件监听器
   * @param {string} event - 事件名
   * @param {function} listener - 监听器函数
   * @param {object} options - 选项
   * @returns {function} 取消监听函数
   */
  on(event, listener, options = {}) {
    if (typeof listener !== 'function') {
      throw new TypeError('Listener must be a function');
    }

    if (!this.events.has(event)) {
      this.events.set(event, []);
    }

    const listeners = this.events.get(event);
    
    // 检查监听器数量限制
    if (listeners.length >= this.maxListeners) {
      console.warn(`Maximum listeners (${this.maxListeners}) exceeded for event: ${event}`);
    }

    // 添加监听器
    const listenerInfo = {
      listener,
      options,
      id: this.generateId()
    };
    
    if (options.prepend) {
      listeners.unshift(listenerInfo);
    } else {
      listeners.push(listenerInfo);
    }

    // 返回取消监听函数
    return () => this.off(event, listener);
  }

  /**
   * 移除事件监听器
   * @param {string} event - 事件名
   * @param {function} listener - 监听器函数
   * @returns {boolean} 是否成功移除
   */
  off(event, listener) {
    if (!this.events.has(event)) {
      return false;
    }

    const listeners = this.events.get(event);
    const index = listeners.findIndex(item => item.listener === listener);
    
    if (index !== -1) {
      listeners.splice(index, 1);
      
      // 如果没有监听器了，删除事件
      if (listeners.length === 0) {
        this.events.delete(event);
      }
      
      return true;
    }

    return false;
  }

  /**
   * 添加一次性事件监听器
   * @param {string} event - 事件名
   * @param {function} listener - 监听器函数
   * @param {object} options - 选项
   * @returns {Promise} Promise对象
   */
  once(event, listener, options = {}) {
    return new Promise((resolve) => {
      const onceListener = (...args) => {
        this.off(event, onceListener);
        if (listener) {
          const result = listener(...args);
          resolve(result);
        } else {
          resolve(args[0]);
        }
      };
      
      this.on(event, onceListener, options);
    });
  }

  /**
   * 触发事件
   * @param {string} event - 事件名
   * @param {...any} args - 参数
   * @returns {boolean} 是否有监听器处理了事件
   */
  emit(event, ...args) {
    let hasListeners = false;

    // 触发具体事件的监听器
    if (this.events.has(event)) {
      const listeners = [...this.events.get(event)]; // 复制数组避免在遍历时修改
      hasListeners = true;

      listeners.forEach(({ listener, options }) => {
        try {
          // 异步执行监听器
          if (options.async) {
            setTimeout(() => listener(...args), 0);
          } else {
            listener(...args);
          }
        } catch (error) {
          console.error(`Error in event listener for "${event}":`, error);
          this.emit('error', error, event, listener);
        }
      });
    }

    // 触发通配符监听器
    if (this.events.has('*')) {
      const wildcardListeners = [...this.events.get('*')];
      hasListeners = true;

      wildcardListeners.forEach(({ listener, options }) => {
        try {
          if (options.async) {
            setTimeout(() => listener(event, ...args), 0);
          } else {
            listener(event, ...args);
          }
        } catch (error) {
          console.error(`Error in wildcard listener for "${event}":`, error);
        }
      });
    }

    return hasListeners;
  }

  /**
   * 移除所有监听器
   * @param {string} event - 事件名，如果不提供则移除所有事件的监听器
   */
  removeAllListeners(event) {
    if (event) {
      this.events.delete(event);
    } else {
      this.events.clear();
    }
  }

  /**
   * 获取事件的监听器数量
   * @param {string} event - 事件名
   * @returns {number} 监听器数量
   */
  listenerCount(event) {
    return this.events.has(event) ? this.events.get(event).length : 0;
  }

  /**
   * 获取所有事件名
   * @returns {Array} 事件名数组
   */
  eventNames() {
    return Array.from(this.events.keys());
  }

  /**
   * 设置最大监听器数量
   * @param {number} max - 最大数量
   */
  setMaxListeners(max) {
    this.maxListeners = max;
  }

  /**
   * 获取最大监听器数量
   * @returns {number} 最大数量
   */
  getMaxListeners() {
    return this.maxListeners;
  }

  /**
   * 生成唯一ID
   * @returns {string} 唯一ID
   */
  generateId() {
    return Math.random().toString(36).substring(2, 11);
  }

  /**
   * 调试信息
   */
  debug() {
    console.group('EventManager Debug Info');
    console.log('Events:', this.events);
    console.log('Event Names:', this.eventNames());
    console.log('Total Events:', this.events.size);
    
    this.events.forEach((listeners, event) => {
      console.log(`Event "${event}": ${listeners.length} listeners`);
    });
    
    console.groupEnd();
  }
}

/**
 * DOM事件工具类
 */
export class DOMEventUtils {
  /**
   * 添加DOM事件监听器
   * @param {Element} element - 元素
   * @param {string} event - 事件名
   * @param {function} handler - 处理函数
   * @param {object} options - 选项
   * @returns {function} 移除监听器函数
   */
  static on(element, event, handler, options = {}) {
    element.addEventListener(event, handler, options);
    return () => element.removeEventListener(event, handler, options);
  }

  /**
   * 移除DOM事件监听器
   * @param {Element} element - 元素
   * @param {string} event - 事件名
   * @param {function} handler - 处理函数
   * @param {object} options - 选项
   */
  static off(element, event, handler, options = {}) {
    element.removeEventListener(event, handler, options);
  }

  /**
   * 添加一次性DOM事件监听器
   * @param {Element} element - 元素
   * @param {string} event - 事件名
   * @param {function} handler - 处理函数
   * @param {object} options - 选项
   * @returns {Promise} Promise对象
   */
  static once(element, event, handler, options = {}) {
    return new Promise((resolve) => {
      const onceHandler = (e) => {
        element.removeEventListener(event, onceHandler, options);
        if (handler) {
          const result = handler(e);
          resolve(result);
        } else {
          resolve(e);
        }
      };
      
      element.addEventListener(event, onceHandler, options);
    });
  }

  /**
   * 事件委托
   * @param {Element} container - 容器元素
   * @param {string} selector - 选择器
   * @param {string} event - 事件名
   * @param {function} handler - 处理函数
   * @returns {function} 移除监听器函数
   */
  static delegate(container, selector, event, handler) {
    const delegateHandler = (e) => {
      const target = e.target.closest(selector);
      if (target && container.contains(target)) {
        handler.call(target, e);
      }
    };
    
    container.addEventListener(event, delegateHandler);
    return () => container.removeEventListener(event, delegateHandler);
  }

  /**
   * 触发自定义事件
   * @param {Element} element - 元素
   * @param {string} eventName - 事件名
   * @param {any} detail - 事件详情
   * @param {object} options - 选项
   */
  static trigger(element, eventName, detail = null, options = {}) {
    const event = new CustomEvent(eventName, {
      detail,
      bubbles: true,
      cancelable: true,
      ...options
    });
    
    element.dispatchEvent(event);
  }
}

/**
 * 方案B事件定义
 * 定义了方案B中使用的所有事件类型
 */
export const SchemeB_Events = {
  // 消息相关事件
  MESSAGE: {
    ADDED: 'message:added',
    UPDATED: 'message:updated',
    DELETED: 'message:deleted',
    COPIED: 'message:copied',
    EDIT_START: 'message:edit:start',
    EDIT_SAVE: 'message:edit:save',
    EDIT_CANCEL: 'message:edit:cancel',
    SEARCH: 'message:search',
    SEARCH_RESULTS: 'message:search:results'
  },

  // AI回复相关事件（方案B：内嵌在版本中）
  AI_REPLY: {
    GENERATION_START: 'ai-reply:generation:start',
    GENERATION_PROGRESS: 'ai-reply:generation:progress',
    GENERATION_COMPLETE: 'ai-reply:generation:complete',
    GENERATION_ERROR: 'ai-reply:generation:error',
    GENERATION_CANCELLED: 'ai-reply:generation:cancelled',
    STATUS_CHANGED: 'ai-reply:status:changed',
    CONTENT_UPDATED: 'ai-reply:content:updated',
    REGENERATE: 'ai-reply:regenerate',
    RETRY: 'ai-reply:retry'
  },

  // 版本管理相关事件
  VERSION: {
    SWITCHED: 'version:switched',
    JUMPED: 'version:jumped',
    CREATED: 'version:created',
    DELETED: 'version:deleted',
    DUPLICATED: 'version:duplicated',
    HISTORY_REQUESTED: 'version:history:requested'
  },

  // 存储相关事件
  STORAGE: {
    SAVE_START: 'storage:save:start',
    SAVE_COMPLETE: 'storage:save:complete',
    SAVE_ERROR: 'storage:save:error',
    LOAD_START: 'storage:load:start',
    LOAD_COMPLETE: 'storage:load:complete',
    LOAD_ERROR: 'storage:load:error',
    MIGRATION_START: 'storage:migration:start',
    MIGRATION_COMPLETE: 'storage:migration:complete',
    BACKUP_CREATED: 'storage:backup:created',
    BACKUP_RESTORED: 'storage:backup:restored'
  },

  // UI相关事件
  UI: {
    THEME_CHANGED: 'ui:theme:changed',
    MODAL_OPEN: 'ui:modal:open',
    MODAL_CLOSE: 'ui:modal:close',
    NOTIFICATION_SHOW: 'ui:notification:show',
    NOTIFICATION_HIDE: 'ui:notification:hide',
    WINDOW_RESIZE: 'ui:window:resize',
    SCROLL_TO_BOTTOM: 'ui:scroll:bottom'
  },

  // 应用生命周期事件
  APP: {
    INIT_START: 'app:init:start',
    INIT_COMPLETE: 'app:init:complete',
    READY: 'app:ready',
    ERROR: 'app:error',
    SHUTDOWN: 'app:shutdown'
  }
};

/**
 * 事件验证器
 * 验证事件是否符合方案B的规范
 */
export class EventValidator {
  /**
   * 验证事件名称是否有效
   * @param {string} eventName - 事件名称
   * @returns {boolean} 是否有效
   */
  static isValidEvent(eventName) {
    const allEvents = Object.values(SchemeB_Events)
      .flatMap(category => Object.values(category));

    return allEvents.includes(eventName) || eventName === '*' || eventName === 'error';
  }

  /**
   * 验证事件数据是否符合规范
   * @param {string} eventName - 事件名称
   * @param {any} eventData - 事件数据
   * @returns {object} 验证结果
   */
  static validateEventData(eventName, eventData) {
    const result = {
      isValid: true,
      errors: [],
      warnings: []
    };

    // 消息相关事件验证
    if (eventName.startsWith('message:')) {
      if (eventName === SchemeB_Events.MESSAGE.ADDED ||
          eventName === SchemeB_Events.MESSAGE.UPDATED) {
        if (!eventData || !eventData.message) {
          result.isValid = false;
          result.errors.push('消息事件必须包含message字段');
        } else if (!eventData.message.isUser) {
          result.warnings.push('方案B中应该只有用户消息事件');
        }
      }
    }

    // AI回复相关事件验证
    if (eventName.startsWith('ai-reply:')) {
      if (!eventData || !eventData.messageId) {
        result.isValid = false;
        result.errors.push('AI回复事件必须包含messageId字段');
      }

      if (eventData && eventData.aiReply && !eventData.aiReply.id) {
        result.warnings.push('AI回复对象应该包含id字段');
      }
    }

    // 版本相关事件验证
    if (eventName.startsWith('version:')) {
      if (!eventData || !eventData.messageId) {
        result.isValid = false;
        result.errors.push('版本事件必须包含messageId字段');
      }
    }

    return result;
  }
}

/**
 * 增强的事件管理器
 * 在原有EventManager基础上添加方案B特定功能
 */
export class SchemeBEventManager extends EventManager {
  constructor() {
    super();
    this.eventHistory = [];
    this.maxHistorySize = 100;
    this.enableValidation = true;
  }

  /**
   * 触发事件（重写以添加验证）
   * @param {string} event - 事件名
   * @param {...any} args - 参数
   * @returns {boolean} 是否有监听器处理了事件
   */
  emit(event, ...args) {
    // 事件验证
    if (this.enableValidation) {
      if (!EventValidator.isValidEvent(event)) {
        console.warn(`未知事件类型: ${event}`);
      }

      if (args.length > 0) {
        const validation = EventValidator.validateEventData(event, args[0]);
        if (!validation.isValid) {
          console.error(`事件数据验证失败 (${event}):`, validation.errors);
          return false;
        }
        if (validation.warnings.length > 0) {
          console.warn(`事件数据警告 (${event}):`, validation.warnings);
        }
      }
    }

    // 记录事件历史
    this.recordEvent(event, args);

    // 调用父类方法
    return super.emit(event, ...args);
  }

  /**
   * 记录事件历史
   * @param {string} event - 事件名
   * @param {Array} args - 参数
   */
  recordEvent(event, args) {
    const eventRecord = {
      event,
      args: args.length > 0 ? JSON.parse(JSON.stringify(args)) : [],
      timestamp: Date.now(),
      id: this.generateId()
    };

    this.eventHistory.push(eventRecord);

    // 限制历史记录大小
    if (this.eventHistory.length > this.maxHistorySize) {
      this.eventHistory.shift();
    }
  }

  /**
   * 获取事件历史
   * @param {string} eventFilter - 事件过滤器（可选）
   * @param {number} limit - 限制数量（可选）
   * @returns {Array} 事件历史
   */
  getEventHistory(eventFilter = null, limit = null) {
    let history = this.eventHistory;

    if (eventFilter) {
      history = history.filter(record =>
        record.event === eventFilter || record.event.startsWith(eventFilter)
      );
    }

    if (limit) {
      history = history.slice(-limit);
    }

    return history.map(record => ({
      ...record,
      formattedTime: new Date(record.timestamp).toLocaleTimeString()
    }));
  }

  /**
   * 清空事件历史
   */
  clearEventHistory() {
    this.eventHistory = [];
  }

  /**
   * 启用/禁用事件验证
   * @param {boolean} enabled - 是否启用
   */
  setValidationEnabled(enabled) {
    this.enableValidation = enabled;
  }

  /**
   * 批量触发事件
   * @param {Array} events - 事件数组 [{event, args}, ...]
   */
  emitBatch(events) {
    events.forEach(({ event, args = [] }) => {
      this.emit(event, ...args);
    });
  }

  /**
   * 调试信息（重写以包含方案B信息）
   */
  debug() {
    super.debug();
    console.group('Scheme B Event Manager Debug');
    console.log('Event History Count:', this.eventHistory.length);
    console.log('Validation Enabled:', this.enableValidation);
    console.log('Recent Events:', this.getEventHistory(null, 10));
    console.groupEnd();
  }
}

// 创建全局事件管理器实例
export const globalEventManager = new SchemeBEventManager();

// 导出默认实例
export default globalEventManager;
